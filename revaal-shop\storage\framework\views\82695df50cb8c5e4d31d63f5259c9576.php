<?php $__env->startSection('title', 'جزئیات محصول'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-xl-12">
                        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title">جزئیات محصول</h4>
                <a href="<?php echo e(route('admin.products.index')); ?>" class="btn btn-primary">
                    <i class="fas fa-arrow-right"></i>
                    بازگشت به لیست محصولات
                </a>
            </div>
                            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6">
                        <h5>نام محصول</h5>
                        <p><?php echo e($product->name); ?></p>
                    </div>
                    <div class="col-lg-6">
                        <h5>دسته‌بندی</h5>
                        <p><?php echo e($product->category->name ?? 'بدون دسته‌بندی'); ?></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <h5>قیمت (تومان)</h5>
                        <p><?php echo e(number_format($product->price)); ?></p>
                                </div>
                    <div class="col-lg-6">
                        <h5>قیمت بدون تخفیف (تومان)</h5>
                        <p><?php echo e(number_format($product->no_offer_price)); ?></p>
                                            </div>
                                        </div>
                <div class="row">
                    <div class="col-lg-6">
                        <h5>موجودی</h5>
                        <p><?php echo e($product->inventory); ?></p>
                                    </div>
                    <div class="col-lg-6">
                        <h5>نوع محصول</h5>
                        <p><?php echo e($product->type); ?></p>
                                            </div>
                                        </div>
                <div class="row">
                    <div class="col-lg-12">
                        <h5>توضیحات</h5>
                        <p><?php echo e($product->description); ?></p>
                                    </div>
                                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <h5>تصویر اصلی محصول</h5>
                        <div class="text-center">
                            <img src="<?php echo e(asset($product->image)); ?>" alt="<?php echo e($product->name); ?>" class="img-fluid rounded" style="max-height: 400px;">
                                    </div>
                                </div>
                                </div>
                <?php if($product->other_image): ?>
                <div class="row mt-4">
                    <div class="col-lg-12">
                        <h5>تصاویر دیگر</h5>
                        <div class="row">
                            <?php $__currentLoopData = json_decode($product->other_image); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                <div class="card">
                                    <img src="<?php echo e(asset($image)); ?>" alt="<?php echo e($product->name); ?>" class="card-img-top" style="height: 200px; object-fit: cover;">
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                <div class="row">
                    <div class="col-lg-6">
                        <h5>وضعیت</h5>
                        <p>
                            <span class="badge bg-<?php echo e($product->status == 'active' ? 'success' : 'danger'); ?>">
                                <?php echo e($product->status == 'active' ? 'فعال' : 'غیرفعال'); ?>

                            </span>
                        </p>
                    </div>
                    <div class="col-lg-6">
                        <h5>محصول ویژه</h5>
                        <p>
                            <span class="badge bg-<?php echo e($product->is_special ? 'warning' : 'secondary'); ?>">
                                <?php echo e($product->is_special ? 'بله' : 'خیر'); ?>

                            </span>
                        </p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <h5>توضیحات متا</h5>
                        <p><?php echo e($product->meta_description); ?></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <h5>کلمات کلیدی</h5>
                        <p><?php echo e($product->meta_keywords); ?></p>
                    </div>
                </div>

                <?php if($product->options && $product->options->count() > 0): ?>
                <div class="row mt-4">
                    <div class="col-lg-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>گزینه‌های محصول</h5>
                            <a href="<?php echo e(route('admin.products.edit', $product->id)); ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit"></i> ویرایش گزینه‌ها
                            </a>
                        </div>
                        <div class="d-flex flex-wrap gap-3">
                            <?php $__currentLoopData = $product->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="border rounded p-3 d-flex flex-column align-items-center" style="min-width:200px; min-height:180px; background:#f9f9f9; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                    <div class="mb-2 fw-bold text-center"><?php echo e($option->name); ?></div>
                                    <div class="mb-2 text-center">
                                        <?php if($option->color_name): ?>
                                            <span class="d-inline-flex align-items-center">
                                                <?php if($option->color_code): ?>
                                                    <span style="display:inline-block;width:20px;height:20px;background:<?php echo e($option->color_code); ?>;border-radius:50%;border:2px solid #ddd;margin-left:8px;"></span>
                                                <?php endif; ?>
                                                <span class="badge bg-light text-dark"><?php echo e($option->color_name); ?></span>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">بدون رنگ</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="mb-2 text-center">
                                        <?php if($option->size): ?>
                                            <span class="badge bg-info fs-6"><?php echo e(strtoupper($option->size)); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">بدون سایز</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="mb-2 text-center">
                                        <span class="text-secondary">قیمت:</span> 
                                        <span class="fw-bold text-success"><?php echo e(number_format($option->price)); ?> تومان</span>
                                    </div>
                                    <div class="mb-2 text-center">
                                        <span class="text-secondary">موجودی:</span> 
                                        <span class="badge bg-<?php echo e($option->inventory > 0 ? 'success' : 'danger'); ?> fs-6"><?php echo e($option->inventory); ?></span>
                                    </div>
                                    <?php if($option->note): ?>
                                        <div class="mb-2 text-center">
                                            <span class="text-secondary">یادداشت:</span> 
                                            <span class="small"><?php echo e($option->note); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="row mt-4">
                    <div class="col-lg-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>گزینه‌های محصول</h5>
                            <a href="<?php echo e(route('admin.products.edit', $product->id)); ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> افزودن گزینه
                            </a>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            این محصول هیچ گزینه‌ای ندارد. برای افزودن گزینه‌های مختلف (سایز، رنگ و ...) روی دکمه "افزودن گزینه" کلیک کنید.
                        </div>
                    </div>
                </div>
                <?php endif; ?>

            </div>
                            </div>
                        </div>
                    </div>
<?php $__env->stopSection(); ?>
                        <h5>گزینه‌های محصول</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="bg-light">
                                    <tr>
                                        <th>نام گزینه</th>
                                        <th>رنگ</th>
                                        <th>کد رنگ</th>
                                        <th>سایز</th>
                                        <th>قیمت (تومان)</th>
                                        <th>موجودی</th>
                                        <th>یادداشت</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $product->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($option->name); ?></td>
                                        <td>
                                            <?php if($option->color_name): ?>
                                                <span class="d-flex align-items-center">
                                                    <?php if($option->color_code): ?>
                                                        <span class="me-2" style="width: 20px; height: 20px; background-color: <?php echo e($option->color_code); ?>; border: 1px solid #ddd; border-radius: 3px;"></span>
                                                    <?php endif; ?>
                                                    <?php echo e($option->color_name); ?>

                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($option->color_code): ?>
                                                <code><?php echo e($option->color_code); ?></code>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($option->size): ?>
                                                <span class="badge bg-info"><?php echo e(strtoupper($option->size)); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e(number_format($option->price)); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo e($option->inventory > 0 ? 'success' : 'danger'); ?>">
                                                <?php echo e($option->inventory); ?>

                                            </span>
                                        </td>
                                        <td><?php echo e($option->note ?? '-'); ?></td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

            </div>
                            </div>
                        </div>
                    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\revaal-shop\revaal-shop\revaal-shop\resources\views/admin/products/show.blade.php ENDPATH**/ ?>